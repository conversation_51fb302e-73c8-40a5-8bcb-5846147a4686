# 公务员与事业单位招聘信息网站整理项目提示词

## 项目目标
整理全国各省份、地市的公务员和事业单位招聘信息发布网站，建立完整的招聘信息获取渠道数据库。

## 数据存储结构

### 文件组织架构
```
PublicInstitutionRecruitmentInformation/
├── 提示词.md                          # 项目指导文档
├── 进度控制.md                        # 项目进度管理
├── 基础数据/
│   ├── 全国地市列表.md                # 省份-地市层级结构
│   └── 典型招聘单位名称.md            # 各类招聘单位分类表
└── 地市数据/
    ├── 北京市/
    │   └── 北京市招聘信息网站.md      # 北京市所有招聘网站
    ├── 上海市/
    │   └── 上海市招聘信息网站.md      # 上海市所有招聘网站
    ├── 广东省/
    │   ├── 广州市招聘信息网站.md      # 广州市招聘网站
    │   ├── 深圳市招聘信息网站.md      # 深圳市招聘网站
    │   └── ...                       # 其他地市
    └── ...                           # 其他省份
```

### 数据文件规范

#### 1. 基础数据文件
- **全国地市列表.md**：省份-地市-县区的完整层级结构
- **典型招聘单位名称.md**：各类招聘单位的标准名称和分类（作为搜索参考）

#### 2. 地市数据文件
- **命名规则**：`{地市名称}招聘信息网站.md`
- **存储位置**：`地市数据/{省份名称}/{地市名称}招聘信息网站.md`
- **内容结构**：该地市所有公务员和事业单位招聘网站信息

## 工作流程

### 第一阶段：基础信息收集
1. **省份/地市列表整理**
   - 收集全国34个省级行政区（23个省、5个自治区、4个直辖市、2个特别行政区）
   - 整理各省份下属地级市、县级市信息
   - **输出文件**：`基础数据/全国地市列表.md`
   - **文件格式**：
     ```markdown
     # 全国地市列表
     ## 直辖市
     ### 北京市
     - 东城区、西城区、朝阳区...

     ## 省份
     ### 广东省
     #### 地级市
     - 广州市、深圳市、珠海市...
     #### 县级市
     - 英德市、连州市、普宁市...
     ```

2. **招聘单位类型分类**
   - **公务员招聘单位**：
     - 省级公务员局/人事厅
     - 地市级人事局/公务员局
     - 县区级人事局
   - **事业单位招聘单位**：
     - 教育系统（教育局、各级学校）
     - 卫生系统（卫健委、医院）
     - 文化系统（文旅局、图书馆、博物馆）
     - 科技系统（科技局、科研院所）
     - 其他事业单位（水利、交通、环保等）
   - **输出文件**：`基础数据/典型招聘单位名称.md`
   - **文件格式**：
     ```markdown
     # 典型招聘单位名称分类
     ## 公务员招聘单位
     ### 省级单位
     - XX省人力资源和社会保障厅
     - XX省公务员局

     ### 地市级单位
     - XX市人力资源和社会保障局
     - XX市公务员局

     ## 事业单位招聘单位
     ### 教育系统
     - XX省教育厅
     - XX市教育局
     - XX大学、XX中学
     ```

### 第二阶段：网站信息搜索
1. **地市事业单位名称搜索**
   - **搜索目标**：每个地市的具体事业单位名称
   - **搜索关键词**：
     - "{地市名称} + 教育局"、"{地市名称} + 卫健委"
     - "{地市名称} + 事业单位名录"、"{地市名称} + 政府机构"
     - "{地市名称} + 医院"、"{地市名称} + 学校"
   - **信息收集**：该地市的具体事业单位名称列表

2. **招聘网站搜索策略**
   - **公务员招聘搜索**：
     - "{地市名称} + 公务员招聘"、"{地市名称} + 人事考试"
     - "{地市名称} + 公务员局"、"{地市名称} + 人社局"
   - **事业单位招聘搜索**：
     - 基于典型招聘单位名称 + 该地市具体事业单位名称
     - "{具体单位名称} + 招聘"、"{具体单位名称} + 官网"
   - **官方网站识别**：优先选择政府官方网站（.gov.cn域名）
   - **招聘页面定位**：在官网中找到专门的招聘信息发布页面

3. **数据收集与存储**
   - **存储位置**：`地市数据/{省份名称}/{地市名称}招聘信息网站.md`
   - **文件格式**：
     ```markdown
     # {地市名称}招聘信息网站

     ## 基本信息
     - **地区**：{省份} > {地市}
     - **数据收集时间**：2025-01-10
     - **数据有效性**：已验证

     ## 该地市事业单位名称列表
     ### 教育系统
     - {地市}教育局
     - {地市}第一中学
     - {地市}职业技术学院
     - ...

     ### 卫生系统
     - {地市}卫生健康委员会
     - {地市}人民医院
     - {地市}中医院
     - ...

     ### 其他系统
     - {地市}文化和旅游局
     - {地市}科技局
     - ...

     ## 公务员招聘网站
     ### {单位名称}
     - **单位网站**：[网站名称](网址)
     - **招聘页面**：[招聘页面标题](招聘页面网址)
     - **更新频率**：日常/定期/不定期
     - **备注**：相关说明信息

     ## 事业单位招聘网站
     ### 教育系统
     #### {具体教育单位名称}
     - **单位网站**：[网站名称](网址)
     - **招聘页面**：[招聘页面标题](招聘页面网址)
     - **更新频率**：日常/定期/不定期

     ### 卫生系统
     #### {具体卫生单位名称}
     - **单位网站**：[网站名称](网址)
     - **招聘页面**：[招聘页面标题](招聘页面网址)
     ```

4. **信息验证**
   - 确认网站的官方性和权威性
   - 验证招聘信息的时效性和更新频率
   - 检查网站的可访问性

### 第三阶段：质量控制与验证
1. **数据完整性检查**
   - 确保每个地市文件包含完整的事业单位名称列表
   - 验证公务员和事业单位招聘网站信息完整
   - 检查所有网址的可访问性

2. **信息准确性验证**
   - 确认招聘页面确实发布招聘信息
   - 验证网站的官方性和权威性
   - 标注信息收集时间和数据有效性

3. **数据标准化**
   - 统一文件格式和命名规范
   - 标准化单位名称和网站描述
   - 确保数据结构的一致性

## 搜索关键词模板

### 公务员招聘搜索词
- "{省份/地市}公务员考试网"
- "{省份/地市}人事考试网"
- "{省份/地市}公务员局官网"
- "{省份/地市}人力资源和社会保障局"

### 事业单位名称搜索词
- "{地市}事业单位名录"
- "{地市}政府机构设置"
- "{地市}教育局"、"{地市}卫健委"
- "{地市}医院"、"{地市}学校"

### 事业单位招聘搜索词
- "{具体单位名称}招聘"
- "{具体单位名称}官网"
- "{地市}事业单位招聘"
- "{地市}人事考试中心"

## 注意事项
1. **数据准确性**：确保所有网址和信息的准确性
2. **时效性**：标注信息收集时间，定期更新
3. **完整性**：尽可能覆盖所有地级市和主要招聘单位
4. **可用性**：确保收集的网站和页面可正常访问
5. **分类清晰**：明确区分公务员和事业单位招聘信息

## 数据文件管理规范

### 文件命名规则
1. **基础数据文件**：使用中文描述性名称
   - `全国地市列表.md`
   - `典型招聘单位名称.md`

2. **地市数据文件**：`{地市名称}招聘信息网站.md`
   - 示例：`北京市招聘信息网站.md`、`广州市招聘信息网站.md`

### 目录结构管理
- **基础数据**：存放基础参考信息，为后续搜索提供依据
- **地市数据**：按省份分组，每个地市独立文件，便于维护和更新

### 数据更新维护
- 每个文件包含数据收集时间和有效性标注
- 建立定期更新机制，确保数据时效性
- 记录数据变更历史，便于追溯和回滚

## 工作重点说明

### 地市事业单位搜索的重要性
- **典型招聘单位名称**：提供搜索参考和分类框架
- **地市具体单位搜索**：每个地市的事业单位名称和设置都不同
- **双重搜索策略**：先搜索该地市的事业单位名称，再搜索对应的招聘网站
- **完整性保证**：确保不遗漏该地市的重要招聘单位

## 预期成果
- **结构化数据库**：完整的全国公务员和事业单位招聘信息网站数据库
- **双层数据结构**：基础数据 > 地市数据的清晰层次
- **地市特色体现**：每个地市的具体事业单位名称和招聘网站
- **可维护系统**：支持增量更新和数据验证的管理体系
- **权威信息源**：为求职者提供官方、及时的招聘信息获取渠道
