# 公务员与事业单位招聘信息网站整理项目提示词

## 项目目标
整理全国各省份、地市的公务员和事业单位招聘信息发布网站，建立完整的招聘信息获取渠道数据库。

## 数据存储结构

### 文件组织架构
```
PublicInstitutionRecruitmentInformation/
├── 提示词.md                          # 项目指导文档
├── 进度控制.md                        # 项目进度管理
├── 基础数据/
│   ├── 全国地市列表.md                # 省份-地市层级结构
│   └── 典型招聘单位名称.md            # 各类招聘单位分类表
├── 地市数据/
│   ├── 北京市/
│   │   └── 北京市招聘信息网站.md      # 北京市所有招聘网站
│   ├── 上海市/
│   │   └── 上海市招聘信息网站.md      # 上海市所有招聘网站
│   ├── 广东省/
│   │   ├── 广州市招聘信息网站.md      # 广州市招聘网站
│   │   ├── 深圳市招聘信息网站.md      # 深圳市招聘网站
│   │   └── ...                       # 其他地市
│   └── ...                           # 其他省份
└── 汇总数据/
    ├── 全国公务员招聘网站汇总.md      # 按省份汇总的公务员招聘网站
    ├── 全国事业单位招聘网站汇总.md    # 按省份汇总的事业单位招聘网站
    └── 招聘信息获取指南.md            # 最终用户使用指南
```

### 数据文件规范

#### 1. 基础数据文件
- **全国地市列表.md**：省份-地市-县区的完整层级结构
- **典型招聘单位名称.md**：各类招聘单位的标准名称和分类

#### 2. 地市数据文件
- **命名规则**：`{地市名称}招聘信息网站.md`
- **存储位置**：`地市数据/{省份名称}/{地市名称}招聘信息网站.md`
- **内容结构**：该地市所有公务员和事业单位招聘网站信息

#### 3. 汇总数据文件
- **全国公务员招聘网站汇总.md**：按省份整理的公务员招聘网站
- **全国事业单位招聘网站汇总.md**：按省份整理的事业单位招聘网站
- **招聘信息获取指南.md**：面向用户的最终使用文档

## 工作流程

### 第一阶段：基础信息收集
1. **省份/地市列表整理**
   - 收集全国34个省级行政区（23个省、5个自治区、4个直辖市、2个特别行政区）
   - 整理各省份下属地级市、县级市信息
   - **输出文件**：`基础数据/全国地市列表.md`
   - **文件格式**：
     ```markdown
     # 全国地市列表
     ## 直辖市
     ### 北京市
     - 东城区、西城区、朝阳区...

     ## 省份
     ### 广东省
     #### 地级市
     - 广州市、深圳市、珠海市...
     #### 县级市
     - 英德市、连州市、普宁市...
     ```

2. **招聘单位类型分类**
   - **公务员招聘单位**：
     - 省级公务员局/人事厅
     - 地市级人事局/公务员局
     - 县区级人事局
   - **事业单位招聘单位**：
     - 教育系统（教育局、各级学校）
     - 卫生系统（卫健委、医院）
     - 文化系统（文旅局、图书馆、博物馆）
     - 科技系统（科技局、科研院所）
     - 其他事业单位（水利、交通、环保等）
   - **输出文件**：`基础数据/典型招聘单位名称.md`
   - **文件格式**：
     ```markdown
     # 典型招聘单位名称分类
     ## 公务员招聘单位
     ### 省级单位
     - XX省人力资源和社会保障厅
     - XX省公务员局

     ### 地市级单位
     - XX市人力资源和社会保障局
     - XX市公务员局

     ## 事业单位招聘单位
     ### 教育系统
     - XX省教育厅
     - XX市教育局
     - XX大学、XX中学
     ```

### 第二阶段：网站信息搜索
1. **搜索策略**
   - 关键词组合："{地市名称} + 公务员招聘"、"{地市名称} + 事业单位招聘"
   - 官方网站识别：优先选择政府官方网站（.gov.cn域名）
   - 招聘页面定位：在官网中找到专门的招聘信息发布页面

2. **数据收集与存储**
   - **存储位置**：`地市数据/{省份名称}/{地市名称}招聘信息网站.md`
   - **文件格式**：
     ```markdown
     # {地市名称}招聘信息网站

     ## 基本信息
     - **地区**：{省份} > {地市}
     - **数据收集时间**：2025-01-10
     - **数据有效性**：已验证

     ## 公务员招聘网站
     ### {单位名称}
     - **单位网站**：[网站名称](网址)
     - **招聘页面**：[招聘页面标题](招聘页面网址)
     - **更新频率**：日常/定期/不定期
     - **备注**：相关说明信息

     ## 事业单位招聘网站
     ### 教育系统
     #### {教育局名称}
     - **单位网站**：[网站名称](网址)
     - **招聘页面**：[招聘页面标题](招聘页面网址)
     - **更新频率**：日常/定期/不定期

     ### 卫生系统
     #### {卫健委名称}
     - **单位网站**：[网站名称](网址)
     - **招聘页面**：[招聘页面标题](招聘页面网址)
     ```

3. **信息验证**
   - 确认网站的官方性和权威性
   - 验证招聘信息的时效性和更新频率
   - 检查网站的可访问性

### 第三阶段：数据整理输出
1. **汇总文件生成**
   - **全国公务员招聘网站汇总.md**
     - 存储位置：`汇总数据/全国公务员招聘网站汇总.md`
     - 内容：按省份汇总所有公务员招聘网站
     - 格式：省份 > 地市 > 公务员单位的层级结构

   - **全国事业单位招聘网站汇总.md**
     - 存储位置：`汇总数据/全国事业单位招聘网站汇总.md`
     - 内容：按省份和系统分类汇总事业单位招聘网站
     - 格式：省份 > 地市 > 系统分类 > 具体单位

   - **招聘信息获取指南.md**
     - 存储位置：`汇总数据/招聘信息获取指南.md`
     - 内容：面向用户的使用指南和快速查询索引
     - 格式：用户友好的查询指南和常用网站推荐

2. **数据处理流程**
   - 从各地市数据文件中提取信息
   - 按照省份和单位类型进行分类汇总
   - 生成统计报表和数据概览
   - 创建用户查询索引和使用指南

3. **质量控制要求**
   - 确保所有网址可正常访问
   - 验证招聘页面确实发布招聘信息
   - 标注信息收集时间和数据有效性
   - 检查数据完整性和一致性

## 搜索关键词模板

### 公务员招聘搜索词
- "{省份/地市}公务员考试网"
- "{省份/地市}人事考试网"
- "{省份/地市}公务员局官网"
- "{省份/地市}人力资源和社会保障局"

### 事业单位招聘搜索词
- "{省份/地市}事业单位招聘"
- "{省份/地市}人事考试中心"
- "{省份/地市}教育局招聘"
- "{省份/地市}卫健委招聘"

## 注意事项
1. **数据准确性**：确保所有网址和信息的准确性
2. **时效性**：标注信息收集时间，定期更新
3. **完整性**：尽可能覆盖所有地级市和主要招聘单位
4. **可用性**：确保收集的网站和页面可正常访问
5. **分类清晰**：明确区分公务员和事业单位招聘信息

## 数据文件管理规范

### 文件命名规则
1. **基础数据文件**：使用中文描述性名称
   - `全国地市列表.md`
   - `典型招聘单位名称.md`

2. **地市数据文件**：`{地市名称}招聘信息网站.md`
   - 示例：`北京市招聘信息网站.md`、`广州市招聘信息网站.md`

3. **汇总数据文件**：使用功能性描述名称
   - `全国公务员招聘网站汇总.md`
   - `全国事业单位招聘网站汇总.md`
   - `招聘信息获取指南.md`

### 目录结构管理
- **基础数据**：存放基础参考信息，为后续搜索提供依据
- **地市数据**：按省份分组，每个地市独立文件，便于维护和更新
- **汇总数据**：最终用户使用的整合数据，提供多种查询视角

### 数据更新维护
- 每个文件包含数据收集时间和有效性标注
- 建立定期更新机制，确保数据时效性
- 记录数据变更历史，便于追溯和回滚

## 预期成果
- **结构化数据库**：完整的全国公务员和事业单位招聘信息网站数据库
- **分层数据文件**：基础数据 > 地市数据 > 汇总数据的三层结构
- **用户友好界面**：便于查询和使用的指南文档
- **可维护系统**：支持增量更新和数据验证的管理体系
- **权威信息源**：为求职者提供官方、及时的招聘信息获取渠道
