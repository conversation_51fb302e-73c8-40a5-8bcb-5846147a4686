# 🗂️ [项目名称] - Public方法/变量协调字典

## 🎯 阶段说明
- **第一阶段**: [阶段描述] ⏳待开始/🔄进行中/✅已完成
- **第二阶段**: [阶段描述] ⏳待开始/🔄进行中/✅已完成
- **第三阶段**: [阶段描述] ⏳待开始/🔄进行中/✅已完成
- **第四阶段**: [阶段描述] ⏳待开始/🔄进行中/✅已完成

## 📚 字典说明

### 🎯 核心目的
统一管理多文件间的Public方法和变量命名，避免开发过程中的调用混乱和命名冲突。

### 🔄 使用流程
1. **步骤开始前**: 仔细阅读字典中已有的类、方法、变量定义
2. **开发过程中**: 严格按照字典中的命名和接口进行开发
3. **步骤完成后**: 立即将新增的public方法和变量添加到字典中
4. **命名冲突**: 如发现命名冲突，优先使用字典中已定义的名称

### ⚠️ 重要规则
- **每个步骤开始前必须读取此字典**
- **每个步骤完成后必须更新此字典**
- **严格按照字典中的方法签名进行调用**
- **新增方法必须包含完整的参数和返回值类型**

---

## 📋 类和方法字典

### 1. [类名]类 ([文件路径])

**类描述**: [类的功能描述]

#### Public方法
```python
def __init__(self, [参数列表]) -> None:
    """初始化[类名]"""

def [方法名](self, [参数列表]) -> [返回类型]:
    """[方法功能描述]"""

# 更多方法...
```

#### Public属性
```python
[属性名]: [属性类型]  # [属性描述]
[属性名]: [属性类型]  # [属性描述]
```

#### 信号定义（如果是PyQt类）
```python
[信号名] = pyqtSignal([参数类型])  # [信号描述]
```

---

### 2. [类名]类 ([文件路径])

**类描述**: [类的功能描述]

#### Public方法
```python
def __init__(self, [参数列表]) -> None:
    """初始化[类名]"""

def [方法名](self, [参数列表]) -> [返回类型]:
    """[方法功能描述]"""
```

#### Public属性
```python
[属性名]: [属性类型]  # [属性描述]
```

---

## 🔗 类间调用关系图

### 依赖关系
```
[主程序]
  └── [主要类]
      ├── [依赖类1]
      ├── [依赖类2]
      └── [依赖类3]
          └── [子依赖类]

[独立类1]
  └── (独立模块)

[独立类2]
  └── (独立模块)
```

### 调用流程
```
1. [主程序] 启动应用
2. [主要类] 初始化
3. [主要类] 创建 [依赖类] 实例
4. [用户操作] 触发功能
5. [主要类] 调用 [依赖类].[方法名]() 方法
6. 返回结果处理
```

---

## 📝 更新记录

### 字典更新日志
| 时间 | 步骤 | 更新内容 | 更新者 |
|------|------|----------|--------|
| [日期] | [步骤号] | [更新内容描述] | AI |
| [日期] | [步骤号] | [更新内容描述] | AI |

### 命名规范
- **类名**: 使用PascalCase (如: ConfigManager)
- **方法名**: 使用snake_case (如: load_config)
- **属性名**: 使用snake_case (如: config_path)
- **常量名**: 使用UPPER_CASE (如: DEFAULT_CONFIG)
- **私有方法**: 以下划线开头 (如: _load_env)

---

## 🚨 AI更新指令

### 每完成一个步骤后必须执行：

1. **读取当前字典**: 确保了解已有的类的方法和变量定义
2. **更新对应类的方法和属性**: 添加新开发的public方法和属性
3. **检查命名冲突**: 确保新增的命名不与现有的冲突
4. **更新调用关系**: 如有新的类间调用关系，更新关系图
5. **记录更新日志**: 在更新记录中添加本次更新的内容

### 更新格式示例：

```python
def method_name(self, param1: str, param2: int = 0) -> bool:
    """
    方法功能描述
    
    Args:
        param1 (str): 参数1描述
        param2 (int, optional): 参数2描述. Defaults to 0.
    
    Returns:
        bool: 返回值描述
    """
```

---

## 🚀 [当前阶段]开发规则

### 📋 [当前阶段]：[阶段描述] ([开始日期] 开始)

#### 🎯 开发目标
[详细描述当前阶段的开发目标和要实现的功能]

#### 🗂️ 协调字典使用规范（[当前阶段]专用）

**每个步骤开始前必须执行：**
1. **读取完整字典** - 仔细阅读所有已定义的类、方法、变量
2. **理解现有架构** - 明确前面阶段已有的类结构和调用关系
3. **规划新类接口** - 设计新类与现有类的集成方式
4. **避免命名冲突** - 确保新增的命名不与现有的冲突

**每个步骤完成后必须执行：**
1. **立即更新字典** - 将新开发的类完整添加到字典中
2. **详细记录接口** - 包含所有public方法的完整签名和参数说明
3. **更新调用关系** - 如有新的类间调用，更新依赖关系图
4. **记录更新日志** - 在字典更新记录中添加详细的更新内容

#### 🚨 [当前阶段]强制规则
- **步骤开始**: 必须先读取字典，了解现有类的接口定义
- **开发过程**: 严格按照字典中的方法签名进行调用
- **步骤结束**: 必须先更新字典，再在进度控制文件中标记完成
- **命名统一**: 新增类必须遵循现有的命名规范
- **接口兼容**: 确保新类与现有类的无缝集成

#### 📊 [当前阶段]预期新增类
预计将新增以下核心类（具体实现后更新）：
- [类名1] ([文件路径])
- [类名2] ([文件路径])
- [类名3] ([文件路径])

**🔄 更新提醒：以上类列表将在实际开发过程中根据具体实现情况进行调整和完善。**

---

**📌 重要提醒：此字典是多文件协调开发的核心工具，必须严格维护，确保所有开发都基于此字典进行！**

**🚨 [当前阶段]开发规则：每个步骤开始前必须读取此字典，每个步骤完成后必须立即更新对应类的方法和属性定义！**
